import React, { useState } from "react";
import { BarChart, Box, Factory, Search, MessageCircle, Grid3X3, Menu } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import UserMenu from "../../components/right-menu/UserMenu";
import AuthModal from "../../components/common/AuthModal";
import { ChatPageStaticData } from "../../data/ChatPageStaticData";
import { saveSearchToHistory } from "../../utils/searchHistory";
import { ChatInterface, ChatHistory, MobileChatHistory } from "../../components/chat";
import { useChat } from "../../hooks/useChat";

const ChatPage: React.FC = () => {
  const [modalOpen, setModalOpen] = useState(false);
  const [step, setStep] = useState<
    "register" | "otp" | "login" | "forget-password" | "reset-password"
  >("login");
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<'chat' | 'categories'>('chat');
  const [mobileHistoryOpen, setMobileHistoryOpen] = useState(false);

  const navigate = useNavigate();
  const chat = useChat();

  const handleSearch = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // Save the original search term to history
      saveSearchToHistory(searchTerm);

      // Navigate with the original search term
      navigate(`/products?search=${encodeURIComponent(searchTerm)}`);
      setSearchTerm("");
    }
  };

  const handleChatSearch = (query: string) => {
    if (query.trim()) {
      saveSearchToHistory(query);
      navigate(`/products?search=${encodeURIComponent(query)}`);
    }
  };

  const closeModal = () => setModalOpen(false);

  const iconMapping = {
    Box: Box,
    Factory: Factory,
    BarChart: BarChart,
  };

  return (
    <>
      <div className="min-h-screen w-full flex flex-col bg-white">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <div className="flex items-center gap-4">
            {viewMode === 'chat' && (
              <button
                onClick={() => setMobileHistoryOpen(true)}
                className="lg:hidden p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                <Menu className="w-5 h-5 text-gray-600" />
              </button>
            )}
            <img
              src="/assets/large-logo.svg"
              alt="Large Logo"
              className="h-8 sm:h-10 w-auto"
            />
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold text-gray-800">InvendoraAI</h1>
              <p className="text-sm text-gray-600">AI-powered product discovery</p>
            </div>
          </div>

          {/* View Toggle */}
          <div className="flex items-center gap-2 mr-4">
            <button
              onClick={() => setViewMode('chat')}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                viewMode === 'chat'
                  ? 'bg-pink-100 text-pink-600'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
            >
              <MessageCircle className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('categories')}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                viewMode === 'categories'
                  ? 'bg-pink-100 text-pink-600'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
            >
              <Grid3X3 className="w-5 h-5" />
            </button>
          </div>

          <UserMenu onSignInClick={() => setModalOpen(true)} />
        </div>

        {/* Main Content */}
        <div className="flex-1 flex">
          {viewMode === 'chat' ? (
            /* Chat Interface with History */
            <div className="flex-1 flex">
              <ChatHistory
                sessions={chat.sessions}
                currentSessionId={chat.currentSession?.id}
                onSessionSelect={chat.switchToSession}
                onNewSession={chat.startNewConversation}
                onDeleteSession={chat.removeSession}
                className="w-80 hidden lg:flex"
              />
              <div className="flex-1">
                <ChatInterface
                  messages={chat.messages}
                  onSendMessage={chat.sendMessage}
                  onSearchClick={handleChatSearch}
                  isLoading={chat.isLoading}
                  isTyping={chat.isTyping}
                />
              </div>
            </div>
          ) : (
            /* Categories View */
            <div className="flex-1 overflow-y-auto p-6">
              {/* Search bar for categories view */}
              <div className="w-full max-w-2xl mx-auto mb-10">
                <form onSubmit={handleSearch} className="relative w-full">
                  <div className="bg-gradient-to-r from-pink-400 via-fuchsia-300 to-yellow-200 p-[2px] rounded-2xl shadow-md">
                    <input
                      type="text"
                      className="w-full text-sm sm:text-base md:text-lg px-4 sm:px-6 pr-[100px] py-3 sm:py-4 bg-white rounded-[1rem] focus:outline-none focus:ring-4 focus:ring-pink-100 transition-all duration-300"
                      placeholder="What wholesale vendors are you looking for..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>

                  {/* Search Button */}
                  <button
                    type="submit"
                    disabled={!searchTerm.trim()}
                    className="absolute right-2 top-1/2 -translate-y-1/2 bg-gradient-to-br from-pink-500 to-pink-300 text-white rounded-xl px-4 sm:px-6 py-2 text-sm sm:text-base hover:from-pink-600 hover:to-pink-400 transition-all duration-300 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    <Search className="w-4 h-4 mr-2" />
                    Search
                  </button>
                </form>
              </div>

              {/* Static category grid */}
              <div className="w-full max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 sm:gap-6 mb-16">
                {ChatPageStaticData.categories.map((category, index) => (
                  <div
                    key={index}
                    className="bg-white rounded-2xl shadow-sm hover:shadow-md transition p-4 sm:p-5 min-h-[360px] flex flex-col justify-between"
                  >
                    <div className="flex items-center mb-4 font-semibold text-base sm:text-lg">
                      <span className="mr-2 text-lg sm:text-xl text-pink-500">
                        {React.createElement(
                          iconMapping[category.icon as keyof typeof iconMapping],
                          {
                            className: "w-5 h-5",
                          }
                        )}
                      </span>
                      {category.title}
                    </div>
                    <div className="space-y-3">
                      {category.items.map((item, i) => (
                        <Link
                          to={`/products?search=${encodeURIComponent(
                            item.title.trim().toLowerCase()
                          )}`}
                          key={i}
                          className="flex items-center space-x-3"
                        >
                          <img
                            src={item.image}
                            alt={item.title}
                            className="w-12 h-12 sm:w-14 sm:h-14 rounded-xl object-cover"
                          />
                          <p className="text-sm text-gray-800 line-clamp-2">
                            {item.title}
                          </p>
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={modalOpen}
        onClose={closeModal}
        step={step}
        setStep={setStep}
      />

      {/* Mobile Chat History */}
      <MobileChatHistory
        isOpen={mobileHistoryOpen}
        onClose={() => setMobileHistoryOpen(false)}
        sessions={chat.sessions}
        currentSessionId={chat.currentSession?.id}
        onSessionSelect={chat.switchToSession}
        onNewSession={chat.startNewConversation}
        onDeleteSession={chat.removeSession}
      />
    </>
  );
};

export default ChatPage;
