import React from 'react';
import { ChatSession } from '../../types/Chat';
import { X, MessageCircle, Calendar } from 'lucide-react';

interface MobileChatHistoryProps {
  isOpen: boolean;
  onClose: () => void;
  sessions: ChatSession[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onDeleteSession: (sessionId: string) => void;
}

const MobileChatHistory: React.FC<MobileChatHistoryProps> = ({
  isOpen,
  onClose,
  sessions,
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onDeleteSession,
}) => {
  if (!isOpen) return null;

  const handleSessionSelect = (sessionId: string) => {
    onSessionSelect(sessionId);
    onClose();
  };

  const handleNewSession = () => {
    onNewSession();
    onClose();
  };

  // Group sessions by date (simplified for mobile)
  const recentSessions = sessions.slice(0, 10);

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className="absolute right-0 top-0 h-full w-80 bg-white shadow-xl transform transition-transform">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="font-semibold text-gray-800">Chat History</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* New Chat Button */}
        <div className="p-4 border-b border-gray-200">
          <button
            onClick={handleNewSession}
            className="w-full px-4 py-3 bg-gradient-to-br from-pink-500 to-purple-600 text-white rounded-lg font-medium transition-all duration-200 hover:from-pink-600 hover:to-purple-700 flex items-center justify-center gap-2"
          >
            <MessageCircle className="w-4 h-4" />
            New Chat
          </button>
        </div>

        {/* Sessions List */}
        <div className="flex-1 overflow-y-auto chat-scrollbar">
          {recentSessions.length === 0 ? (
            <div className="p-4 text-center text-gray-500">
              <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No conversations yet</p>
            </div>
          ) : (
            <div className="p-2">
              {recentSessions.map((session) => (
                <div
                  key={session.id}
                  onClick={() => handleSessionSelect(session.id)}
                  className={`p-3 mb-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    currentSessionId === session.id
                      ? 'bg-pink-100 border border-pink-200'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <h3 className="text-sm font-medium text-gray-800 truncate">
                    {session.title}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                    {session.context.messages.length > 0
                      ? session.context.messages[session.context.messages.length - 1].content
                      : 'No messages yet'
                    }
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    <Calendar className="w-3 h-3 text-gray-400" />
                    <span className="text-xs text-gray-400">
                      {new Date(session.updatedAt).toLocaleDateString()}
                    </span>
                    <span className="text-xs text-gray-400">
                      {session.context.messages.filter(m => m.type === 'user').length} messages
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MobileChatHistory;
