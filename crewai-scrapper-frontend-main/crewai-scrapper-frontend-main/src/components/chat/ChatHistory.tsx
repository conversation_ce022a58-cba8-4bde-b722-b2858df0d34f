import React, { useState } from 'react';
import { ChatSession } from '../../types/Chat';
import { 
  MessageCircle, 
  Plus, 
  Trash2, 
  MoreVertical, 
  Calendar,
  Search as SearchIcon
} from 'lucide-react';

interface ChatHistoryProps {
  sessions: ChatSession[];
  currentSessionId?: string;
  onSessionSelect: (sessionId: string) => void;
  onNewSession: () => void;
  onDeleteSession: (sessionId: string) => void;
  className?: string;
}

const ChatHistory: React.FC<ChatHistoryProps> = ({
  sessions,
  currentSessionId,
  onSessionSelect,
  onNewSession,
  onDeleteSession,
  className = '',
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  // Filter sessions based on search term
  const filteredSessions = sessions.filter(session =>
    session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    session.context.messages.some(msg => 
      msg.content.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // Group sessions by date
  const groupedSessions = filteredSessions.reduce((groups, session) => {
    const date = new Date(session.updatedAt);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    let groupKey: string;
    if (date.toDateString() === today.toDateString()) {
      groupKey = 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      groupKey = 'Yesterday';
    } else if (date.getTime() > today.getTime() - 7 * 24 * 60 * 60 * 1000) {
      groupKey = 'This Week';
    } else {
      groupKey = date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    
    if (!groups[groupKey]) {
      groups[groupKey] = [];
    }
    groups[groupKey].push(session);
    return groups;
  }, {} as Record<string, ChatSession[]>);

  const handleDeleteClick = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    onDeleteSession(sessionId);
    setActiveDropdown(null);
  };

  const toggleDropdown = (e: React.MouseEvent, sessionId: string) => {
    e.stopPropagation();
    setActiveDropdown(activeDropdown === sessionId ? null : sessionId);
  };

  return (
    <div className={`bg-gray-50 border-r border-gray-200 flex flex-col ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h2 className="font-semibold text-gray-800">Chat History</h2>
          <button
            onClick={onNewSession}
            className="p-2 bg-pink-500 hover:bg-pink-600 text-white rounded-lg transition-colors duration-200"
            title="New Chat"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
        
        {/* Search */}
        <div className="relative">
          <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search conversations..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* Sessions List */}
      <div className="flex-1 overflow-y-auto chat-scrollbar">
        {Object.keys(groupedSessions).length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
            <p className="text-sm">No conversations yet</p>
            <button
              onClick={onNewSession}
              className="mt-2 text-pink-500 hover:text-pink-600 text-sm font-medium"
            >
              Start your first chat
            </button>
          </div>
        ) : (
          Object.entries(groupedSessions).map(([groupName, groupSessions]) => (
            <div key={groupName} className="mb-4">
              <div className="px-4 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider flex items-center gap-2">
                <Calendar className="w-3 h-3" />
                {groupName}
              </div>
              
              {groupSessions.map((session) => (
                <div
                  key={session.id}
                  onClick={() => onSessionSelect(session.id)}
                  className={`mx-2 mb-1 p-3 rounded-lg cursor-pointer transition-all duration-200 relative group hover:scale-[1.02] ${
                    currentSessionId === session.id
                      ? 'bg-pink-100 border border-pink-200 shadow-md'
                      : 'hover:bg-white hover:shadow-sm'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-800 truncate">
                        {session.title}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                        {session.context.messages.length > 0
                          ? session.context.messages[session.context.messages.length - 1].content
                          : 'No messages yet'
                        }
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs text-gray-400">
                          {new Date(session.updatedAt).toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </span>
                        <span className="text-xs text-gray-400">
                          {session.context.messages.filter(m => m.type === 'user').length} messages
                        </span>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <button
                        onClick={(e) => toggleDropdown(e, session.id)}
                        className="p-1 opacity-0 group-hover:opacity-100 hover:bg-gray-200 rounded transition-all duration-200"
                      >
                        <MoreVertical className="w-4 h-4 text-gray-500" />
                      </button>
                      
                      {activeDropdown === session.id && (
                        <div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 min-w-[120px]">
                          <button
                            onClick={(e) => handleDeleteClick(e, session.id)}
                            className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2 rounded-lg"
                          >
                            <Trash2 className="w-3 h-3" />
                            Delete
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default ChatHistory;
