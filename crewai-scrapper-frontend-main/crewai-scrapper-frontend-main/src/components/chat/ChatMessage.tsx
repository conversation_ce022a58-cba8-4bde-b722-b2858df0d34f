import React from 'react';
import { ChatMessage as ChatMessageType } from '../../types/Chat';
import { User, Bot, Search } from 'lucide-react';

interface ChatMessageProps {
  message: ChatMessageType;
  onSearchClick?: (query: string) => void;
}

const ChatMessage: React.FC<ChatMessageProps> = ({ message, onSearchClick }) => {
  const isUser = message.type === 'user';
  
  return (
    <div className={`flex gap-3 mb-4 animate-fade-in ${isUser ? 'justify-end' : 'justify-start'}`}>
      {!isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-pink-500 to-purple-600 flex items-center justify-center shadow-md">
            <Bot className="w-4 h-4 text-white" />
          </div>
        </div>
      )}
      
      <div className={`max-w-[80%] ${isUser ? 'order-first' : ''}`}>
        <div
          className={`px-4 py-3 rounded-2xl shadow-sm transition-all duration-200 hover:shadow-md ${
            isUser
              ? 'bg-gradient-to-br from-pink-500 to-purple-600 text-white ml-auto'
              : 'bg-gray-100 text-gray-800 hover:bg-gray-50'
          }`}
        >
          <p className="text-sm leading-relaxed">{message.content}</p>
          
          {/* Show search query if available */}
          {message.searchQuery && !isUser && (
            <div className="mt-3 pt-3 border-t border-gray-200">
              <div className="flex items-center gap-2 text-xs text-gray-600">
                <Search className="w-3 h-3" />
                <span>Search suggestion:</span>
              </div>
              <button
                onClick={() => onSearchClick?.(message.searchQuery!)}
                className="mt-1 px-3 py-1 bg-pink-100 hover:bg-pink-200 text-pink-700 text-xs rounded-full transition-colors duration-200 flex items-center gap-1"
              >
                <Search className="w-3 h-3" />
                {message.searchQuery}
              </button>
            </div>
          )}
        </div>
        
        <div className={`text-xs text-gray-500 mt-1 ${isUser ? 'text-right' : 'text-left'}`}>
          {new Date(message.timestamp).toLocaleTimeString([], { 
            hour: '2-digit', 
            minute: '2-digit' 
          })}
        </div>
      </div>
      
      {isUser && (
        <div className="flex-shrink-0">
          <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center">
            <User className="w-4 h-4 text-gray-600" />
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatMessage;
