import { AIResponse, ChatMessage } from '../types/Chat';
import { ConversationContextManager } from '../utils/conversationContext';

/**
 * Mock AI service for development and testing
 * This simulates the AI assistant responses until the real backend is ready
 */
export class MockAIService {
  private static responses = {
    greetings: [
      "Hello! I'm here to help you find the perfect products. What are you looking for today?",
      "Hi there! I'm your shopping assistant. Tell me what you need and I'll help you find it.",
      "Welcome! I can help you discover amazing products. What's on your shopping list?"
    ],
    
    clarifications: [
      "Could you tell me more about what you're looking for?",
      "What specific features are important to you?",
      "Do you have any preferences for color, size, or style?",
      "What's your budget range for this item?"
    ],
    
    confirmations: [
      "Great choice! Let me search for that for you.",
      "Perfect! I'll find the best options for you.",
      "Excellent! Searching for the perfect match now.",
      "Got it! Let me find what you're looking for."
    ]
  };

  /**
   * Process a user message and return an AI response
   */
  static async processMessage(
    userMessage: string, 
    conversationHistory: ChatMessage[]
  ): Promise<AIResponse> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const contextualQuery = ConversationContextManager.buildContextualQuery([
      ...conversationHistory,
      {
        id: 'temp',
        content: userMessage,
        type: 'user',
        timestamp: Date.now()
      }
    ]);

    // Determine response type based on context
    const responseType = this.determineResponseType(userMessage, conversationHistory);
    const message = this.generateResponse(responseType, userMessage, contextualQuery.query);
    
    // Generate suggestions
    const suggestions = ConversationContextManager.suggestFollowUps(contextualQuery.components);

    return {
      message,
      refinedSearchQuery: contextualQuery.query,
      confidence: contextualQuery.confidence,
      suggestions: suggestions.length > 0 ? suggestions : undefined
    };
  }

  /**
   * Determine what type of response to give
   */
  private static determineResponseType(
    userMessage: string, 
    history: ChatMessage[]
  ): 'greeting' | 'clarification' | 'confirmation' | 'search' {
    const lowerMessage = userMessage.toLowerCase();
    
    // First message or greeting
    if (history.filter(m => m.type === 'user').length === 0 || 
        lowerMessage.includes('hello') || 
        lowerMessage.includes('hi') || 
        lowerMessage.includes('hey')) {
      return 'greeting';
    }

    // Check if message has enough detail for search
    const contextualQuery = ConversationContextManager.buildContextualQuery([
      ...history,
      {
        id: 'temp',
        content: userMessage,
        type: 'user',
        timestamp: Date.now()
      }
    ]);

    if (contextualQuery.confidence >= 0.5) {
      return 'confirmation';
    } else if (contextualQuery.confidence >= 0.2) {
      return 'clarification';
    } else {
      return 'clarification';
    }
  }

  /**
   * Generate appropriate response message
   */
  private static generateResponse(
    type: 'greeting' | 'clarification' | 'confirmation' | 'search',
    userMessage: string,
    searchQuery: string
  ): string {
    switch (type) {
      case 'greeting':
        return this.getRandomResponse(this.responses.greetings);
      
      case 'clarification':
        if (searchQuery) {
          return `I see you're interested in ${searchQuery}. ${this.getRandomResponse(this.responses.clarifications)}`;
        }
        return this.getRandomResponse(this.responses.clarifications);
      
      case 'confirmation':
        if (searchQuery) {
          return `${this.getRandomResponse(this.responses.confirmations)} I'll search for "${searchQuery}" for you.`;
        }
        return this.getRandomResponse(this.responses.confirmations);
      
      default:
        return "I'm here to help you find what you're looking for. Could you tell me more about what you need?";
    }
  }

  /**
   * Get a random response from an array
   */
  private static getRandomResponse(responses: string[]): string {
    return responses[Math.floor(Math.random() * responses.length)];
  }

  /**
   * Refine a search query based on conversation context
   */
  static async refineSearchQuery(
    query: string, 
    context: string[]
  ): Promise<{ refinedQuery: string; confidence: number }> {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Simple refinement logic
    const combinedContext = [...context, query].join(' ');
    const words = combinedContext.toLowerCase().split(/\s+/);
    
    // Remove duplicates and common words
    const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'];
    const uniqueWords = [...new Set(words)].filter(word => 
      word.length > 2 && !stopWords.includes(word)
    );

    const refinedQuery = uniqueWords.slice(0, 5).join(' ');
    const confidence = Math.min(uniqueWords.length / 5, 1);

    return {
      refinedQuery,
      confidence
    };
  }
}
