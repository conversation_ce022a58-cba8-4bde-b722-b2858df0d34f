import { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../api/store';
import {
  startNewSession,
  addMessage,
  updateMessageSearchQuery,
  setLoading,
  setTyping,
  setError,
  clearError,
  updateCurrentSearchQuery,
  loadSessions,
  setCurrentSession,
  deleteSession,
} from '../api/services/Chat/chatSlice';
import { ChatStorageManager } from '../utils/chatStorage';
import { ConversationContextManager } from '../utils/conversationContext';
import { MockAIService } from '../services/mockAIService';
import { ChatMessage } from '../types/Chat';

export const useChat = () => {
  const dispatch = useDispatch();
  const chatState = useSelector((state: RootState) => state.chat);
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);

  // Load sessions from storage on mount
  useEffect(() => {
    const savedSessions = ChatStorageManager.loadSessions();
    if (savedSessions.length > 0) {
      dispatch(loadSessions(savedSessions));
    } else if (!chatState.currentSession) {
      dispatch(startNewSession());
    }

    // Cleanup old sessions
    ChatStorageManager.cleanupOldSessions();
  }, [dispatch, chatState.currentSession]);

  // Save sessions to storage whenever they change
  useEffect(() => {
    if (chatState.sessions.length > 0) {
      ChatStorageManager.saveSessions(chatState.sessions);
    }
  }, [chatState.sessions]);

  /**
   * Send a message and get AI response
   */
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim()) return;

    dispatch(clearError());
    
    try {
      // Add user message
      dispatch(addMessage({
        content: content.trim(),
        type: 'user'
      }));

      // Set typing indicator
      dispatch(setTyping(true));
      dispatch(setLoading(true));

      // Get conversation history
      const messages = chatState.currentSession?.context.messages || [];
      
      // Process with AI service (using mock for now)
      const aiResponse = await MockAIService.processMessage(content, messages);

      // Add AI response
      const aiMessageAction = addMessage({
        content: aiResponse.message,
        type: 'assistant',
        searchQuery: aiResponse.refinedSearchQuery
      });
      
      dispatch(aiMessageAction);

      // Update current search query if we have a good one
      if (aiResponse.confidence >= 0.3 && aiResponse.refinedSearchQuery) {
        dispatch(updateCurrentSearchQuery(aiResponse.refinedSearchQuery));
      }

    } catch (error) {
      console.error('Failed to send message:', error);
      dispatch(setError('Failed to send message. Please try again.'));
    } finally {
      dispatch(setTyping(false));
      dispatch(setLoading(false));
    }
  }, [dispatch, chatState.currentSession]);

  /**
   * Start a new conversation session
   */
  const startNewConversation = useCallback(() => {
    dispatch(startNewSession());
  }, [dispatch]);

  /**
   * Get the current search query from conversation context
   */
  const getCurrentSearchQuery = useCallback(() => {
    if (!chatState.currentSession) return '';
    
    const contextualQuery = ConversationContextManager.buildContextualQuery(
      chatState.currentSession.context.messages
    );
    
    return contextualQuery.query;
  }, [chatState.currentSession]);

  /**
   * Check if conversation has enough context for search
   */
  const hasEnoughContextForSearch = useCallback(() => {
    if (!chatState.currentSession) return false;
    
    return ConversationContextManager.hasEnoughContext(
      chatState.currentSession.context.messages
    );
  }, [chatState.currentSession]);

  /**
   * Get search suggestions based on current context
   */
  const getSearchSuggestions = useCallback(() => {
    if (!chatState.currentSession) return [];
    
    const contextualQuery = ConversationContextManager.buildContextualQuery(
      chatState.currentSession.context.messages
    );
    
    return ConversationContextManager.suggestFollowUps(contextualQuery.components);
  }, [chatState.currentSession]);

  /**
   * Clear current conversation
   */
  const clearConversation = useCallback(() => {
    startNewConversation();
  }, [startNewConversation]);

  /**
   * Switch to a different session
   */
  const switchToSession = useCallback((sessionId: string) => {
    dispatch(setCurrentSession(sessionId));
  }, [dispatch]);

  /**
   * Delete a session
   */
  const removeSession = useCallback((sessionId: string) => {
    dispatch(deleteSession(sessionId));
    ChatStorageManager.deleteSession(sessionId);
  }, [dispatch]);

  /**
   * Export conversation for sharing or backup
   */
  const exportConversation = useCallback(() => {
    if (!chatState.currentSession) return null;

    return {
      title: chatState.currentSession.title,
      messages: chatState.currentSession.context.messages,
      searchQuery: chatState.currentSession.context.currentSearchQuery,
      exportedAt: new Date().toISOString()
    };
  }, [chatState.currentSession]);

  return {
    // State
    currentSession: chatState.currentSession,
    sessions: chatState.sessions,
    messages: chatState.currentSession?.context.messages || [],
    isLoading: chatState.isLoading,
    isTyping: chatState.isTyping,
    error: chatState.error,
    currentSearchQuery: chatState.currentSession?.context.currentSearchQuery || '',

    // Actions
    sendMessage,
    startNewConversation,
    clearConversation,
    switchToSession,
    removeSession,

    // Utilities
    getCurrentSearchQuery,
    hasEnoughContextForSearch,
    getSearchSuggestions,
    exportConversation,
  };
};
