import { ChatMessage } from '../types/Chat';

export interface ContextualSearchQuery {
  query: string;
  confidence: number;
  components: {
    product?: string;
    attributes?: string[];
    modifiers?: string[];
    category?: string;
  };
}

// Product categories for better context understanding
const PRODUCT_CATEGORIES = [
  'clothing', 'shoes', 'electronics', 'accessories', 'bags', 'jewelry',
  'home', 'furniture', 'kitchen', 'beauty', 'health', 'sports', 'books',
  'toys', 'automotive', 'tools', 'garden', 'pet', 'baby'
];

// Common product attributes
const PRODUCT_ATTRIBUTES = {
  colors: ['red', 'blue', 'green', 'yellow', 'black', 'white', 'pink', 'purple', 'orange', 'brown', 'gray', 'grey', 'navy', 'beige', 'gold', 'silver'],
  sizes: ['small', 'medium', 'large', 'xl', 'xxl', 'xs', 'tiny', 'huge', 'big', 'mini'],
  materials: ['cotton', 'leather', 'silk', 'wool', 'denim', 'plastic', 'metal', 'wood', 'glass', 'ceramic'],
  styles: ['casual', 'formal', 'vintage', 'modern', 'classic', 'trendy', 'elegant', 'sporty', 'bohemian'],
  conditions: ['new', 'used', 'refurbished', 'vintage', 'antique'],
  brands: ['nike', 'adidas', 'apple', 'samsung', 'sony', 'gucci', 'prada', 'zara', 'h&m']
};

// Modifiers that affect search intent
const SEARCH_MODIFIERS = ['cheap', 'expensive', 'affordable', 'premium', 'luxury', 'budget', 'high-quality', 'best', 'top', 'popular'];

export class ConversationContextManager {
  /**
   * Builds a contextual search query from conversation messages
   */
  static buildContextualQuery(messages: ChatMessage[]): ContextualSearchQuery {
    const userMessages = messages.filter(msg => msg.type === 'user');
    
    if (userMessages.length === 0) {
      return {
        query: '',
        confidence: 0,
        components: {}
      };
    }

    // Combine all user messages into a single context
    const combinedText = userMessages.map(msg => msg.content.toLowerCase()).join(' ');
    
    // Extract components
    const components = this.extractComponents(combinedText);
    
    // Build the query
    const query = this.buildQuery(components);
    
    // Calculate confidence based on how specific the query is
    const confidence = this.calculateConfidence(components);

    return {
      query,
      confidence,
      components
    };
  }

  /**
   * Extracts product components from text
   */
  private static extractComponents(text: string): ContextualSearchQuery['components'] {
    const words = text.replace(/[^\w\s]/g, ' ').split(/\s+/).filter(word => word.length > 1);
    
    const components: ContextualSearchQuery['components'] = {
      attributes: [],
      modifiers: []
    };

    // Find product category
    for (const category of PRODUCT_CATEGORIES) {
      if (text.includes(category)) {
        components.category = category;
        break;
      }
    }

    // Find main product (look for nouns that might be products)
    const productKeywords = this.findProductKeywords(words);
    if (productKeywords.length > 0) {
      components.product = productKeywords[0]; // Take the first/most relevant
    }

    // Find attributes
    Object.entries(PRODUCT_ATTRIBUTES).forEach(([type, values]) => {
      values.forEach(value => {
        if (text.includes(value) && !components.attributes?.includes(value)) {
          components.attributes?.push(value);
        }
      });
    });

    // Find modifiers
    SEARCH_MODIFIERS.forEach(modifier => {
      if (text.includes(modifier) && !components.modifiers?.includes(modifier)) {
        components.modifiers?.push(modifier);
      }
    });

    return components;
  }

  /**
   * Finds potential product keywords
   */
  private static findProductKeywords(words: string[]): string[] {
    // Common product terms
    const productTerms = [
      'jeans', 'shirt', 'dress', 'shoes', 'sneakers', 'boots', 'jacket', 'coat',
      'phone', 'laptop', 'computer', 'tablet', 'headphones', 'earbuds', 'speaker',
      'bag', 'backpack', 'purse', 'wallet', 'watch', 'necklace', 'ring', 'bracelet',
      'chair', 'table', 'sofa', 'bed', 'lamp', 'mirror', 'rug', 'curtains',
      'makeup', 'perfume', 'shampoo', 'lotion', 'cream', 'soap',
      'book', 'magazine', 'notebook', 'pen', 'pencil'
    ];

    return words.filter(word => productTerms.includes(word));
  }

  /**
   * Builds a search query from components
   */
  private static buildQuery(components: ContextualSearchQuery['components']): string {
    const queryParts: string[] = [];

    // Add modifiers first
    if (components.modifiers && components.modifiers.length > 0) {
      queryParts.push(...components.modifiers);
    }

    // Add attributes
    if (components.attributes && components.attributes.length > 0) {
      queryParts.push(...components.attributes);
    }

    // Add main product
    if (components.product) {
      queryParts.push(components.product);
    }

    // Add category if no specific product found
    if (!components.product && components.category) {
      queryParts.push(components.category);
    }

    return queryParts.join(' ').trim();
  }

  /**
   * Calculates confidence score based on query specificity
   */
  private static calculateConfidence(components: ContextualSearchQuery['components']): number {
    let score = 0;

    // Base score for having any components
    if (components.product) score += 0.4;
    if (components.category) score += 0.2;
    if (components.attributes && components.attributes.length > 0) score += 0.3;
    if (components.modifiers && components.modifiers.length > 0) score += 0.1;

    // Bonus for specificity
    const totalComponents = (components.attributes?.length || 0) + 
                           (components.modifiers?.length || 0) + 
                           (components.product ? 1 : 0) + 
                           (components.category ? 1 : 0);

    if (totalComponents >= 3) score += 0.2;
    else if (totalComponents >= 2) score += 0.1;

    return Math.min(score, 1.0);
  }

  /**
   * Suggests follow-up questions based on current context
   */
  static suggestFollowUps(components: ContextualSearchQuery['components']): string[] {
    const suggestions: string[] = [];

    if (components.product && !components.attributes?.some(attr => PRODUCT_ATTRIBUTES.colors.includes(attr))) {
      suggestions.push("What color would you prefer?");
    }

    if (components.product && !components.attributes?.some(attr => PRODUCT_ATTRIBUTES.sizes.includes(attr))) {
      suggestions.push("What size are you looking for?");
    }

    if (components.product && !components.modifiers?.length) {
      suggestions.push("What's your budget range?");
    }

    if (!components.product && components.category) {
      suggestions.push(`What specific ${components.category} item are you looking for?`);
    }

    return suggestions.slice(0, 3); // Limit to 3 suggestions
  }

  /**
   * Determines if the conversation has enough context for a search
   */
  static hasEnoughContext(messages: ChatMessage[]): boolean {
    const query = this.buildContextualQuery(messages);
    return query.confidence >= 0.3 && query.query.length > 0;
  }
}
