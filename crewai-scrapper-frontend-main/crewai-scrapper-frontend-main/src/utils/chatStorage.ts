import { ChatSession } from '../types/Chat';

const STORAGE_KEY = 'invendora_chat_sessions';
const MAX_SESSIONS = 50; // Limit stored sessions to prevent storage bloat

export class ChatStorageManager {
  /**
   * Save chat sessions to localStorage
   */
  static saveSessions(sessions: ChatSession[]): void {
    try {
      // Limit the number of sessions stored
      const sessionsToStore = sessions.slice(0, MAX_SESSIONS);
      localStorage.setItem(STORAGE_KEY, JSON.stringify(sessionsToStore));
    } catch (error) {
      console.error('Failed to save chat sessions:', error);
    }
  }

  /**
   * Load chat sessions from localStorage
   */
  static loadSessions(): ChatSession[] {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const sessions = JSON.parse(stored) as ChatSession[];
        // Validate and clean up sessions
        return sessions.filter(this.isValidSession);
      }
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
    }
    return [];
  }

  /**
   * Save a single session
   */
  static saveSession(session: ChatSession): void {
    const sessions = this.loadSessions();
    const existingIndex = sessions.findIndex(s => s.id === session.id);
    
    if (existingIndex !== -1) {
      sessions[existingIndex] = session;
    } else {
      sessions.unshift(session);
    }
    
    this.saveSessions(sessions);
  }

  /**
   * Delete a session
   */
  static deleteSession(sessionId: string): void {
    const sessions = this.loadSessions();
    const filteredSessions = sessions.filter(s => s.id !== sessionId);
    this.saveSessions(filteredSessions);
  }

  /**
   * Clear all sessions
   */
  static clearAllSessions(): void {
    try {
      localStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear chat sessions:', error);
    }
  }

  /**
   * Get session by ID
   */
  static getSession(sessionId: string): ChatSession | null {
    const sessions = this.loadSessions();
    return sessions.find(s => s.id === sessionId) || null;
  }

  /**
   * Validate session structure
   */
  private static isValidSession(session: any): session is ChatSession {
    return (
      session &&
      typeof session.id === 'string' &&
      typeof session.title === 'string' &&
      typeof session.createdAt === 'number' &&
      typeof session.updatedAt === 'number' &&
      session.context &&
      typeof session.context.sessionId === 'string' &&
      Array.isArray(session.context.messages) &&
      Array.isArray(session.context.cumulativeSearchTerms) &&
      typeof session.context.currentSearchQuery === 'string' &&
      typeof session.context.lastUpdated === 'number'
    );
  }

  /**
   * Clean up old sessions (older than 30 days)
   */
  static cleanupOldSessions(): void {
    const sessions = this.loadSessions();
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);
    const recentSessions = sessions.filter(s => s.updatedAt > thirtyDaysAgo);
    
    if (recentSessions.length !== sessions.length) {
      this.saveSessions(recentSessions);
    }
  }

  /**
   * Export sessions for backup
   */
  static exportSessions(): string {
    const sessions = this.loadSessions();
    return JSON.stringify(sessions, null, 2);
  }

  /**
   * Import sessions from backup
   */
  static importSessions(data: string): boolean {
    try {
      const sessions = JSON.parse(data) as ChatSession[];
      if (Array.isArray(sessions) && sessions.every(this.isValidSession)) {
        this.saveSessions(sessions);
        return true;
      }
    } catch (error) {
      console.error('Failed to import chat sessions:', error);
    }
    return false;
  }
}
