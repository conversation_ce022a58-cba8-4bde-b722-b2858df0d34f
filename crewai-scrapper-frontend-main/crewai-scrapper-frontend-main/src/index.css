@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for chat interface */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(236, 72, 153, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(236, 72, 153, 0.6);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Custom scrollbar for chat */
.chat-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.chat-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}



@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Add animation delay for each product card */
.grid > div:nth-child(1) { animation-delay: 0.1s; }
.grid > div:nth-child(2) { animation-delay: 0.2s; }
.grid > div:nth-child(3) { animation-delay: 0.3s; }
.grid > div:nth-child(4) { animation-delay: 0.4s; }
.grid > div:nth-child(5) { animation-delay: 0.5s; }
.grid > div:nth-child(6) { animation-delay: 0.6s; }
.grid > div:nth-child(7) { animation-delay: 0.7s; }
.grid > div:nth-child(8) { animation-delay: 0.8s; }

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background: #555;
  }

  .modal-left {
    flex: 1;
    background: linear-gradient(135deg, #ffffff, #f3f4f6);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 40px 30px;
    border-right: 1px solid #e5e7eb;
    color: #374151;
    user-select: none;
  }

  .modal-left h2.form-title {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 12px;
  }

  .modal-left p.form-subtitle {
    font-size: 16px;
    color: #6b7280;
    line-height: 1.5;
    max-width: 220px;
    text-align: center;
    user-select: none;
  }

  .range-thumb-pink::-webkit-slider-thumb {
    appearance: none;
    height: 16px;
    width: 16px;
    background: #ec4899;
    border-radius: 9999px;
    cursor: pointer;
    margin-top: -6px;
  }
  .range-thumb-pink::-moz-range-thumb {
    height: 16px;
    width: 16px;
    background: #ec4899;
    border-radius: 9999px;
    cursor: pointer;
  }
  
  .range-thumb-yellow::-webkit-slider-thumb {
    appearance: none;
    height: 16px;
    width: 16px;
    background: #facc15;
    border-radius: 9999px;
    cursor: pointer;
    margin-top: -6px;
  }
  .range-thumb-yellow::-moz-range-thumb {
    height: 16px;
    width: 16px;
    background: #facc15;
    border-radius: 9999px;
    cursor: pointer;
  }
  .hero-background {
    position: absolute;
    top: 5rem;
    left: 0;
    width: 100%;
    height: 600px;
    z-index: -10;
    overflow: hidden;
    pointer-events: none;
  }
  
  .arc {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 500px;
    height: 250px;
    border-radius: 50% / 100%;
    transform: translate(-50%, -50%);
    border: solid transparent;
    border-top-width: 40px;
    border-top-style: solid;
    background: none;
    filter: blur(3px);
    opacity: 0.5;
  }
  
  .arc1 {
    width: 520px;
    height: 260px;
    border-top-color: transparent;
    background-image: linear-gradient(90deg, #f8d7da, #f545d4, #fddea2);
    -webkit-mask-image: linear-gradient(to bottom, transparent 40%, black 60%);
    mask-image: linear-gradient(to bottom, transparent 40%, black 60%);
    opacity: 0.6;
  }
  
  .arc2 {
    width: 480px;
    height: 240px;
    top: calc(50% + 10px);
    background-image: linear-gradient(90deg, #e1bee7, #ba68c8, #f545d4);
    opacity: 0.5;
  }
  
  .arc3 {
    width: 440px;
    height: 220px;
    top: calc(50% + 20px);
    background-image: linear-gradient(90deg, #c8e6c9, #81c784, #4caf50);
    opacity: 0.4;
  }
  
  .custom-gradient {
    background: linear-gradient(to bottom, white 25%, #fce7f3 50%, #e9d5ff 100%);
  }
  