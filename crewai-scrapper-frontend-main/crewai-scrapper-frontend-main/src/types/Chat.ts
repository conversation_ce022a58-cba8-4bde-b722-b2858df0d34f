export interface ChatMessage {
  id: string;
  content: string;
  type: 'user' | 'assistant';
  timestamp: number;
  searchQuery?: string; // The refined search query generated from this message
  isTyping?: boolean; // For showing typing indicator
}

export interface ConversationContext {
  sessionId: string;
  messages: ChatMessage[];
  cumulativeSearchTerms: string[]; // Building blocks of search intent
  currentSearchQuery: string; // The current refined search query
  lastUpdated: number;
}

export interface ChatSession {
  id: string;
  title: string; // Auto-generated title based on conversation
  context: ConversationContext;
  createdAt: number;
  updatedAt: number;
}

export interface AIResponse {
  message: string;
  refinedSearchQuery: string;
  confidence: number; // 0-1 confidence in the search query
  suggestions?: string[]; // Suggested follow-up questions or refinements
}

export interface ChatState {
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  isLoading: boolean;
  error: string | null;
  isTyping: boolean;
}
