import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { AIResponse, ChatMessage } from '../../../types/Chat';

export interface ChatRequest {
  message: string;
  conversationHistory: ChatMessage[];
  userId?: string;
}

export interface ChatResponse {
  success: boolean;
  data: AIResponse;
  error?: string;
}

export const chatApi = createApi({
  reducerPath: 'chatApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/chat',
    prepareHeaders: (headers, { getState }) => {
      // Add auth token if available
      const token = (getState() as any).auth.token;
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      headers.set('content-type', 'application/json');
      return headers;
    },
  }),
  endpoints: (builder) => ({
    sendMessage: builder.mutation<ChatResponse, ChatRequest>({
      query: (chatRequest) => ({
        url: '/message',
        method: 'POST',
        body: chatRequest,
      }),
      // Transform the response to handle errors gracefully
      transformResponse: (response: ChatResponse) => {
        if (!response.success) {
          throw new Error(response.error || 'Failed to process message');
        }
        return response;
      },
    }),
    
    refineSearchQuery: builder.mutation<{ refinedQuery: string; confidence: number }, { 
      query: string; 
      context: string[] 
    }>({
      query: ({ query, context }) => ({
        url: '/refine-query',
        method: 'POST',
        body: { query, context },
      }),
    }),
  }),
});

export const { 
  useSendMessageMutation, 
  useRefineSearchQueryMutation 
} = chatApi;
