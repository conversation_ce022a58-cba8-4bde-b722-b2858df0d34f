import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ChatMessage, ChatSession, ChatState, ConversationContext } from '../../../types/Chat';

const initialState: ChatState = {
  currentSession: null,
  sessions: [],
  isLoading: false,
  error: null,
  isTyping: false,
};

// Helper function to generate session title from messages
const generateSessionTitle = (messages: ChatMessage[]): string => {
  const firstUserMessage = messages.find(msg => msg.type === 'user');
  if (firstUserMessage) {
    const words = firstUserMessage.content.split(' ').slice(0, 4);
    return words.join(' ') + (firstUserMessage.content.split(' ').length > 4 ? '...' : '');
  }
  return 'New Conversation';
};

// Helper function to extract search terms from conversation
const extractSearchTerms = (messages: ChatMessage[]): string[] => {
  const terms: string[] = [];
  
  messages.forEach(message => {
    if (message.type === 'user') {
      // Simple keyword extraction - in a real app, this would be more sophisticated
      const words = message.content.toLowerCase()
        .replace(/[^\w\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 2);
      
      // Filter out common words
      const stopWords = ['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use'];
      const relevantWords = words.filter(word => !stopWords.includes(word));
      
      terms.push(...relevantWords);
    }
  });
  
  // Remove duplicates and return unique terms
  return [...new Set(terms)];
};

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    startNewSession: (state) => {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const newSession: ChatSession = {
        id: sessionId,
        title: 'New Conversation',
        context: {
          sessionId,
          messages: [],
          cumulativeSearchTerms: [],
          currentSearchQuery: '',
          lastUpdated: Date.now(),
        },
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
      
      state.currentSession = newSession;
      state.sessions.unshift(newSession);
    },
    
    addMessage: (state, action: PayloadAction<Omit<ChatMessage, 'id' | 'timestamp'>>) => {
      if (!state.currentSession) {
        // Auto-create session if none exists
        chatSlice.caseReducers.startNewSession(state);
      }
      
      if (state.currentSession) {
        const message: ChatMessage = {
          ...action.payload,
          id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          timestamp: Date.now(),
        };
        
        state.currentSession.context.messages.push(message);
        state.currentSession.context.lastUpdated = Date.now();
        state.currentSession.updatedAt = Date.now();
        
        // Update search terms and query
        const searchTerms = extractSearchTerms(state.currentSession.context.messages);
        state.currentSession.context.cumulativeSearchTerms = searchTerms;
        
        // Generate current search query from terms
        if (searchTerms.length > 0) {
          state.currentSession.context.currentSearchQuery = searchTerms.join(' ');
        }
        
        // Update session title if this is the first user message
        if (message.type === 'user' && state.currentSession.context.messages.filter(m => m.type === 'user').length === 1) {
          state.currentSession.title = generateSessionTitle(state.currentSession.context.messages);
        }
        
        // Update the session in the sessions array
        const sessionIndex = state.sessions.findIndex(s => s.id === state.currentSession!.id);
        if (sessionIndex !== -1) {
          state.sessions[sessionIndex] = state.currentSession;
        }
      }
    },
    
    updateMessageSearchQuery: (state, action: PayloadAction<{ messageId: string; searchQuery: string }>) => {
      if (state.currentSession) {
        const message = state.currentSession.context.messages.find(m => m.id === action.payload.messageId);
        if (message) {
          message.searchQuery = action.payload.searchQuery;
          state.currentSession.context.lastUpdated = Date.now();
          state.currentSession.updatedAt = Date.now();
          
          // Update the session in the sessions array
          const sessionIndex = state.sessions.findIndex(s => s.id === state.currentSession!.id);
          if (sessionIndex !== -1) {
            state.sessions[sessionIndex] = state.currentSession;
          }
        }
      }
    },
    
    setCurrentSession: (state, action: PayloadAction<string>) => {
      const session = state.sessions.find(s => s.id === action.payload);
      if (session) {
        state.currentSession = session;
      }
    },
    
    deleteSession: (state, action: PayloadAction<string>) => {
      state.sessions = state.sessions.filter(s => s.id !== action.payload);
      if (state.currentSession?.id === action.payload) {
        state.currentSession = state.sessions[0] || null;
      }
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    updateCurrentSearchQuery: (state, action: PayloadAction<string>) => {
      if (state.currentSession) {
        state.currentSession.context.currentSearchQuery = action.payload;
        state.currentSession.context.lastUpdated = Date.now();
        state.currentSession.updatedAt = Date.now();

        // Update the session in the sessions array
        const sessionIndex = state.sessions.findIndex(s => s.id === state.currentSession!.id);
        if (sessionIndex !== -1) {
          state.sessions[sessionIndex] = state.currentSession;
        }
      }
    },

    loadSessions: (state, action: PayloadAction<ChatSession[]>) => {
      state.sessions = action.payload;
      if (action.payload.length > 0 && !state.currentSession) {
        state.currentSession = action.payload[0];
      }
    },
  },
});

export const {
  startNewSession,
  addMessage,
  updateMessageSearchQuery,
  setCurrentSession,
  deleteSession,
  setLoading,
  setTyping,
  setError,
  clearError,
  updateCurrentSearchQuery,
  loadSessions,
} = chatSlice.actions;

export default chatSlice.reducer;
